package com.interfun.buz.home.view.fragment

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.core.view.doOnPreDraw
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.chat.common.manager.ChatGlobalInfoRecorder
import com.interfun.buz.chat.common.utils.ChatTracker
import com.interfun.buz.chat.common.view.activity.BaseChatActivity
import com.interfun.buz.chat.common.view.activity.ChatHomeActivity
import com.interfun.buz.chat.common.view.block.guidance.AutoPlayTooltip
import com.interfun.buz.chat.common.view.dialog.MergeAccountRemindDialog
import com.interfun.buz.chat.wt.block.WTHomeDialogManageBlock
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.arouter.startActivityByRouter
import com.interfun.buz.common.base.binding.BaseBindingFragment
import com.interfun.buz.common.bean.chat.JumpVoiceCallPageFrom
import com.interfun.buz.common.bean.voicecall.CallConflictState
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.constants.PATH_CHAT_FRAGMENT_HOME_NEW
import com.interfun.buz.common.constants.PATH_CONTACTS_ACTIVITY_CONV_SEARCH
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.eventbus.*
import com.interfun.buz.common.eventbus.HomePageEnum.PageUserSetting
import com.interfun.buz.common.eventbus.contact.PushMergeAccountRemindEvent
import com.interfun.buz.common.eventbus.im5.IMConnectSuccessEvent
import com.interfun.buz.common.eventbus.user.UserProfileUploadSuccessEvent
import com.interfun.buz.common.home.interfaces.IContactShowAddFriendsGuideDialogBlock
import com.interfun.buz.common.ktx.portrait
import com.interfun.buz.common.manager.ABTestManager
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.cache.wt.ChatHomeAFTrackerCache
import com.interfun.buz.common.manager.realtimecall.MinimizeViewModel
import com.interfun.buz.common.manager.user.FriendRequestCountManager
import com.interfun.buz.common.manager.user.FriendStatusManager
import com.interfun.buz.common.service.ContactsService
import com.interfun.buz.common.service.StorageService
import com.interfun.buz.common.utils.CommonTracker
import com.interfun.buz.common.utils.StartupCostTrace
import com.interfun.buz.common.utils.StartupTrace
import com.interfun.buz.common.voicecall.GroupCallStaterImpl
import com.interfun.buz.domain.im.social.repository.SendIMRepository
import com.interfun.buz.domain.im.social.entity.VoiceMsgParams
import com.interfun.buz.domain.voiceemoji_im.manager.VoiceEmojiSignalManager
import com.interfun.buz.home.view.compose.VoiceTestButton
import androidx.compose.foundation.layout.Column
import androidx.compose.runtime.getValue
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.interfun.buz.home.R
import com.interfun.buz.home.databinding.ChatFragmentHomeNewBinding
import com.interfun.buz.home.databinding.HomeFragmentLayoutBottomBinding
import com.interfun.buz.home.databinding.HomeFragmentLayoutCenterBinding
import com.interfun.buz.home.databinding.HomeFragmentLayoutTopBinding
import com.interfun.buz.home.view.block.*
import com.interfun.buz.home.view.block.bot.HomeThinkingHandleBlockNew
import com.interfun.buz.home.view.block.bot.HomeTopicBubbleHandleBlockNew
import com.interfun.buz.home.view.block.bot.HomeTranslatorLangBarBlockNew
import com.interfun.buz.home.view.viewmodel.WTViewModelNew
import com.interfun.buz.im.repo.IMFileDownloadRepository
import com.interfun.buz.liveplace.track.LivePlaceTracker
import com.lizhi.component.basetool.common.AppStateWatcher
import com.lizhi.component.tekiplayer.TekiPlayer
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import javax.inject.Inject

/**
 * <AUTHOR>
 *
 * @date 2022/7/6
 *
 * @desc Host:
 * [com.interfun.buz.chat.common.view.activity.ChatHomeActivity]
 */
@Route(path = PATH_CHAT_FRAGMENT_HOME_NEW)
@AndroidEntryPoint
class ChatHomeFragmentNew : BaseBindingFragment<ChatFragmentHomeNewBinding>() {

    companion object {
        const val TAG = "ChatHomeFragmentNew"
    }

    @Inject
    lateinit var chatFileDownloadRepositoryImpl: IMFileDownloadRepository

    @Inject
    lateinit var sendIMRepository: SendIMRepository

    private val wtViewModel by viewModels<WTViewModelNew>()
    private val minimizeViewModel by activityViewModels<MinimizeViewModel>()

    private val contactService by routerServices<ContactsService>()
    private var hasImConnected = false
    private var hasContactSyncCompleted = false
    private var observerContactLocalChange = false
    private var hasPostFriendCount = false
    private var notifyPermissionCheckBlock: NotifyPermissionCheckBlockNew? = null
    private val onAppForegroundWatcher: (() -> Unit) = {
        logInfo(TAG, "on App Foreground")
        if (observerContactLocalChange || hasContactSyncCompleted.not()) {
            observerContactLocalChange = false
            routerServices<ContactsService>().value?.syncLocalContactsAndUpload(true)
        }
        // Handle cache auto delete
        routerServices<StorageService>().value?.autoDelete()
        CommonTracker.postStartUpPermissionCheckResult(true)
    }
    private var newRegisterGuidanceBlock: WTNewRegisterGuidanceBlockNew? = null
    private lateinit var wtListBlock: WTListBlockNew
    private lateinit var homeSendingMediaBlock: HomeSendingMediaBlockNew
    private lateinit var wtVoiceMojiBlockNew : WTVoiceMojiBlockNew
    private lateinit var wtMoreDialogPanelBlock : WTMoreDialogPanelBlock
    private lateinit var wtQuietModeBlock: WTQuietModeBlockNew
    private lateinit var homeFileBlock: HomeFileBlock
    private val groupCallStarter = GroupCallStaterImpl(this)
    private val source: String? by lazy {
        activity?.intent?.getStringExtra(RouterParamKey.ChatHome.KEY_OPEN_SOURCE)
    }

    private val joinVoiceCallAction: (channelId: Long, groupId: Long, callType: Int, joinResultCallback: ((CallConflictState) -> Unit)?) -> Unit =
        { channelId, groupId, callType, joinResultCallback ->
            groupCallStarter.joinGroupVoiceCall(
                context = requireContext(),
                groupId = groupId,
                callType = callType,
                channelId = channelId,
                source = JumpVoiceCallPageFrom.GROUP_ITEM_CALL_CLICK,
                callback = joinResultCallback
            )
        }

    /**布局区域*/
    val bindingTop by lazy { HomeFragmentLayoutTopBinding.bind(binding.root) }

    val bindingCenter by lazy { HomeFragmentLayoutCenterBinding.bind(binding.root) }

    val bindingBottom by lazy { HomeFragmentLayoutBottomBinding.bind(binding.root) }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        view.doOnPreDraw {
            logInfo(TAG, "ChatHomeFragment doOnPreDraw,${System.currentTimeMillis()}")
            StartupTrace.onMainFrameShow("main")
            StartupCostTrace.reportAppStartUpTime()
        }
    }

    override fun initBlock() {
        /**特有的block*/
        WTGuidanceBlock(this, binding).bind(this)
        showInviteDialogWhenNoPermission()
        newRegisterGuidanceBlock = WTNewRegisterGuidanceBlockNew(this, binding).bind(this)
        wtQuietModeBlock = WTQuietModeBlockNew(this, binding).bind(this)
        wtListBlock = WTListBlockNew(this, binding, joinVoiceCallAction).bind(this)
        HomeRecordUiBlock(this, binding).bind(this)
        HomeRecordBlock(this, binding).bind(this)
        HomeRecordTooltipsBlock(this, newRegisterGuidanceBlock, binding).bind(this)
        HomeInfoBlockNew(this, binding).bind(this)
        HomeMuteGuideTipBlockNew(this, binding).bind(this)
        HomeAiInfoBlockNew(this, binding).bind(this)
        HomeTranslatorLangBarBlockNew(this, binding).bind(this)
        HomeThinkingHandleBlockNew(this, binding).bind(fragment)
        HomeTopicBubbleHandleBlockNew(this, binding).bind(fragment)
        IMStatusChangeBlockNew(this, binding).bind(this)
        CampaignEntryBlockNew(this, binding).bind(this)
        WTVoicePreviewBlock(this,binding).bind(this)
        WTVoiceFilterToolTipsBlock(this, binding).bind(this)
        WTVFGuidanceBlock(this,binding).bind(this)
        notifyPermissionCheckBlock =
            NotifyPermissionCheckBlockNew(this, binding).bind(this)
        WTGroupAddressAiBlock(this, binding).bind(viewLifecycleOwner)
        wtVoiceMojiBlockNew = WTVoiceMojiBlockNew(this, binding).bind(this)
        wtMoreDialogPanelBlock = WTMoreDialogPanelBlock(this, binding).bind(this)
        homeSendingMediaBlock = HomeSendingMediaBlockNew(this, binding, wtViewModel).bind(this)
        WTLivePlaceGuidanceBlock(this, binding).bind(this)

        /**公共的block*/
        WTHomeDialogManageBlock(this, binding).bind(this)
        contactService?.createHomeContactTooltipsBlock(
            this,
            binding.root,
            bindingTop.iftvContact
        )?.bind(this)

        WTQuietGuideTipsBlockNew(this,
            binding
        ).bind(this)
        homeFileBlock = HomeFileBlock(this, binding).bind(this)

        // 临时处理，后续移动到feature初始化层
        VoiceEmojiSignalManager.addSignalObserver()
    }

    // 在WTGuidanceBlock后面，新用户时，保证没有授权通讯录时，弹窗在引导弹窗上面
    private fun showInviteDialogWhenNoPermission() {
        if (fragment.activity is ChatHomeActivity) {
            (fragment.activity as ChatHomeActivity)
                .obtainRegisterInterface2(IContactShowAddFriendsGuideDialogBlock::class.java)?.showInviteWhenNoPermission()
        }
    }

    override fun initView() {
        bindingTop.spaceStatusBar.initStatusBarHeight()
        bindingTop.viewPortraitClickArea.click(vibrate = true) {
            activity?.obtainRegisterInterface(ICompaignOpNew::class.java)?.clickEnterProfile()
            newRegisterGuidanceBlock?.dismissOverlaySettingPrompt()
            BusUtil.post(HomePageChangeEvent(PageUserSetting))
            ChatTracker.onChaHomeMinePortraitClick()
        }
        bindingTop.vContactExpandArea.click(vibrate = true) {
            bindingTop.unReadFriendRequestCount.gone()
            CommonMMKV.lastHomePageShowRequestCount =
                FriendRequestCountManager.countFlow.value.unReadCount
            BusUtil.post(HomePageChangeEvent(HomePageEnum.PageContact))
            ChatTracker.onChaHomeContactClick()
        }
        bindingTop.vifSearchExpandArea.click {
            CommonTracker.onClickHomeSearch("click_search_icon")
            startActivityByRouter(
                PATH_CONTACTS_ACTIVITY_CONV_SEARCH,
                buildPostcardBlock = {
                    withTransition(R.anim.anim_conv_search_enter, R.anim.anim_conv_search_out)
                }
            )
        }
    }



    override fun initData() {
        super.initData()
        // Subscribe to the event of contacts content changed
        BusUtil.observe<ContactLocalChangeEvent>(this) {
            logInfo(TAG, "Subscribe to the event of contacts content changed")
            observerContactLocalChange = true
            hasPostFriendCount = false
        }
        // Subscribe to the event of user profile upload success
        BusUtil.observe<UserProfileUploadSuccessEvent>(this) {
            refreshMyPortrait()
        }
        FriendRequestCountManager.requestCount()

        minimizeViewModel.isMinimizingFlow.collectIn(viewLifecycleOwner) { isMinimizing ->
            MinimizeViewModel.isMinimizing = isMinimizing
            if (isMinimizing) {
                bindingTop.spaceStatusBar.layoutHeight(0.dp)
            } else {
                bindingTop.spaceStatusBar.layoutHeight(fragment.requireContext.statusBarHeight)
            }
        }
        BusUtil.observeSticky<PushMergeAccountRemindEvent>(this) {
            logInfo(TAG, "Subscribe to the event of PushMergeAccountRemindEvent")
            it.type?.let { type ->
                MergeAccountRemindDialog(requireContext).setType(type).show()
                PushMergeAccountRemindEvent.post(null)
            }
        }

        ChatGlobalInfoRecorder.nowChatListTargetId.observe(this){
            if (null != it) onChatActivityVisibleChange(true)
        }

        combine(ChatHomeAFTrackerCache.chatHomeAFLinkHashStateFlow, FriendRequestCountManager.countFlowForTracerShareFlow) { afLinkData, friendCountData ->
            return@combine afLinkData to friendCountData
        }.collectDistinctLatestIn(viewLifecycleOwner) { (afLinkData, friendCountData) ->
            logInfo("combine receive AFLinkStateFlow and countFlow: afLinkData: ${afLinkData}")
            afLinkData?.let {
                ChatHomeAFTrackerCache.clearData()
                ChatTracker.onPageViewAVS2024121301(afLinkHash = afLinkData.linkHash, totalCount = friendCountData.totalCount)
            }
        }

    }

    private fun refreshMyPortrait() {
        bindingTop.ivMinePortrait.setPortrait(UserSessionManager.portrait)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //通讯录的diff同步与上传
        routerServices<ContactsService>().value?.syncLocalContactsAndUpload(false, source)
        // Handle cache auto delete
        routerServices<StorageService>().value?.autoDelete()
        TekiPlayer.enableBuiltinPolicy(false)
        AppStateWatcher.addForegroundWatcher(onAppForegroundWatcher)
        lifecycleScope.launch {
            chatFileDownloadRepositoryImpl.resumeHistoryDownloading()
        }
    }

    override fun onResume() {
        super.onResume()
        onChatActivityVisibleChange(false)
        refreshMyPortrait()
        ChatTracker.onChatHomePageView()
        // After showing notification problem dialog and complete action
        if (CommonMMKV.NotificationProblemDialogIsShow) {
            CommonMMKV.NotificationProblemDialogIsShow = false
            wtViewModel.reportNotificationProblemNotComplete()
            NotificationProblemDialogDismissEvent.post()
        }
        LivePlaceTracker.onAVS2025010205()
    }

    private fun onChatActivityVisibleChange(isVisible:Boolean){
        binding.vHomeMask.visibleIf(isVisible)
        binding.vHomeMask.click {
            if (topActivity is BaseChatActivity
                && AutoPlayTooltip.tooltipsLocation.value == AutoPlayTooltip.LOCATION_NULL) {
                topActivity?.finish()
            }
        }
        val highlightElevation = if (isVisible) 10f else 0f
        bindingTop.viewQuietClickArea.elevation = highlightElevation
        bindingTop.autoPlayToggleView.elevation = highlightElevation
        bindingTop.ivQuiteLight.elevation = highlightElevation
    }

    override fun onDestroy() {
        AppStateWatcher.removeForegroundWatcher(onAppForegroundWatcher)
        super.onDestroy()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        notifyPermissionCheckBlock?.onActivityResult(requestCode, resultCode, data)
    }

    override fun onViewStateRestored(savedInstanceState: Bundle?) {
        super.onViewStateRestored(savedInstanceState)
        wtListBlock.onRestoreInstanceState(savedInstanceState)
        wtQuietModeBlock.onRestoreInstanceState(savedInstanceState)
        homeFileBlock.onRestoreInstanceState(savedInstanceState)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        wtQuietModeBlock.onSaveInstanceState(outState)
        homeFileBlock.onSaveInstanceState(outState)
    }

    /**
     * 从 assets 复制测试音频文件到临时目录
     * @param assetsFileName assets 文件夹中的文件名
     * @return 复制后的文件路径，失败时返回 null
     */
    private suspend fun copyTestAudioFromAssets(assetsFileName: String): String? = withContext(Dispatchers.IO) {
        try {
            val context = requireContext()
            val inputStream = context.assets.open(assetsFileName)

            // 创建临时文件
            val tempDir = File(context.cacheDir, "voice_test")
            if (!tempDir.exists()) {
                tempDir.mkdirs()
            }

            val tempFile = File(tempDir, "test_voice_${System.currentTimeMillis()}.mp3")
            val outputStream = FileOutputStream(tempFile)

            // 复制文件
            inputStream.use { input ->
                outputStream.use { output ->
                    input.copyTo(output)
                }
            }

            logInfo(TAG, "Successfully copied test audio file to: ${tempFile.absolutePath}")
            tempFile.absolutePath
        } catch (e: IOException) {
            logError(TAG, e, "IO error while copying test audio file from assets: $assetsFileName")
            null
        } catch (e: SecurityException) {
            logError(TAG, e, "Security error while copying test audio file: $assetsFileName")
            null
        } catch (e: Exception) {
            logError(TAG, e, "Unexpected error while copying test audio file: $assetsFileName")
            null
        }
    }

    /**
     * 测试语音消息按钮点击事件
     * 发送预定义的测试音频文件作为语音消息
     */
    fun onTestVoiceButtonClick() {
        lifecycleScope.launchIO {

            for (i in 0..100) {

                try {
                    logInfo(TAG, "Test voice button clicked")

                    // 获取当前选中的会话信息
                    val selectedItem = wtViewModel.selectedItemFlow.value
                    if (selectedItem !is com.interfun.buz.home.entity.HomeSelectedItem.Group) {
                        return@launchIO
                    }
                    val targetId = selectedItem.targetId
                    val convType = IM5ConversationType.GROUP

                    if (targetId == null || convType == null) {
                        logWarn(TAG, "No conversation selected for voice test - targetId: $targetId, convType: $convType")
                        toast("请先选择一个会话再测试语音消息")
                        return@launchIO
                    }

                    logInfo(TAG, "Sending test voice message to targetId: $targetId, convType: $convType")

                    // 复制测试音频文件到临时目录
                    val tempFilePath = copyTestAudioFromAssets("temp_test.mp3")
                    if (tempFilePath == null) {
                        logError(TAG, "Failed to copy test audio file from assets")
                        toast("复制测试音频文件失败，请检查文件是否存在")
                        return@launchIO
                    }

                    logInfo(TAG, "Test audio file copied to: $tempFilePath")

                    // 创建 VoiceMsgParams
                    val voiceMsgParams = VoiceMsgParams(
                        targetId = targetId.toString(),
                        convType = convType,
                        path = tempFilePath,
                        duration = 1100 // 1.1秒，按需求设置
                    )

                    // 发送语音消息
                    val result = sendIMRepository.sendVoiceMessage(voiceMsgParams)

                    logInfo(TAG, "Test voice message sent successfully, result: $result")
                    toast("✅ 测试语音消息发送成功")

                    // 清理临时文件
                    try {
                        val tempFile = File(tempFilePath)
                        if (tempFile.exists() && tempFile.delete()) {
                            logInfo(TAG, "Temporary test audio file cleaned up")
                        }
                    } catch (e: Exception) {
                        logWarn(TAG, "Failed to clean up temporary file: ${e.message}")
                    }

                } catch (e: SecurityException) {
                    logError(TAG, e, "Security exception while sending test voice message")
                    toast("❌ 权限不足，无法发送测试语音消息")
                } catch (e: IOException) {
                    logError(TAG, e, "IO exception while processing test voice message")
                    toast("❌ 文件处理失败，请重试")
                } catch (e: Exception) {
                    logError(TAG, e, "Unexpected error while sending test voice message")
                    toast("❌ 发送测试语音消息失败: ${e.message ?: "未知错误"}")
                }
            }
        }
    }
}